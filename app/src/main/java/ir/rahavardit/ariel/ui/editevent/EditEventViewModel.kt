package ir.rahavardit.ariel.ui.editevent

import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.Event
import ir.rahavardit.ariel.data.model.EventGroup
import ir.rahavardit.ariel.data.repository.EventRepository
import ir.rahavardit.ariel.data.repository.NewEventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the edit event screen.
 */
class EditEventViewModel : ViewModel() {

    private val eventRepository = EventRepository()
    private val newEventRepository = NewEventRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _groups = MutableLiveData<List<EventGroup>>()
    val groups: LiveData<List<EventGroup>> = _groups

    private val _selectedFile = MutableLiveData<Uri?>()
    val selectedFile: LiveData<Uri?> = _selectedFile

    private val _eventUpdateResult = MutableLiveData<EventUpdateResult>()
    val eventUpdateResult: LiveData<EventUpdateResult> = _eventUpdateResult

    private val _eventDetails = MutableLiveData<Event>()
    val eventDetails: LiveData<Event> = _eventDetails

    /**
     * Fetches event details by short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event.
     */
    fun fetchEventDetails(token: String, shortUuid: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = eventRepository.getEventDetails(token, shortUuid)

                result.fold(
                    onSuccess = { event ->
                        _eventDetails.value = event
                        _isLoading.value = false
                    },
                    onFailure = { exception ->
                        _error.value = exception.message
                        _isLoading.value = false
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message
                _isLoading.value = false
            }
        }
    }

    /**
     * Fetches the list of available groups.
     *
     * @param token The authentication token.
     */
    fun fetchGroups(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = newEventRepository.getGroups(token)

                result.fold(
                    onSuccess = { groupsList ->
                        _groups.value = groupsList
                        _isLoading.value = false
                    },
                    onFailure = { exception ->
                        _error.value = exception.message
                        _isLoading.value = false
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message
                _isLoading.value = false
            }
        }
    }

    /**
     * Sets the selected file.
     *
     * @param uri The URI of the selected file.
     */
    fun setSelectedFile(uri: Uri?) {
        _selectedFile.value = uri
    }

    /**
     * Validates the input fields.
     *
     * @param title The title of the event.
     * @param body The body content of the event.
     * @param groupId The ID of the group the event belongs to.
     * @return A ValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(title: String, body: String, groupId: Int?): ValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()
        val isGroupValid = groupId != null

        return ValidationResult(isTitleValid, isBodyValid, isGroupValid)
    }

    /**
     * Updates an event.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event to update.
     * @param title The updated title of the event.
     * @param body The updated body content of the event.
     * @param groupId The updated ID of the group the event belongs to.
     * @param context The context to use for file operations (optional).
     */
    fun updateEvent(
        token: String,
        shortUuid: String,
        title: String,
        body: String,
        groupId: Int,
        context: Context? = null
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val fileUri = _selectedFile.value

                val result = if (fileUri != null && context != null) {
                    // Update event with file
                    eventRepository.updateEventWithFile(
                        token, shortUuid, title, body, groupId, fileUri, context
                    )
                } else {
                    // Update event without file
                    eventRepository.updateEvent(token, shortUuid, title, body, groupId)
                }

                result.fold(
                    onSuccess = { event ->
                        _eventUpdateResult.value = EventUpdateResult.Success(event)
                        // Clear selected file after successful update
                        _selectedFile.value = null
                    },
                    onFailure = { exception ->
                        _eventUpdateResult.value = EventUpdateResult.Error(
                            exception.message ?: "Failed to update event"
                        )
                    }
                )

                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message
                _eventUpdateResult.value = EventUpdateResult.Error(
                    e.message ?: "Failed to update event"
                )
                _isLoading.value = false
            }
        }
    }

    /**
     * Data class representing the validation result for the input fields.
     *
     * @property isTitleValid Whether the title is valid.
     * @property isBodyValid Whether the body is valid.
     * @property isGroupValid Whether the group is valid.
     */
    data class ValidationResult(
        val isTitleValid: Boolean,
        val isBodyValid: Boolean,
        val isGroupValid: Boolean
    )

    /**
     * Sealed class representing the result of updating an event.
     */
    sealed class EventUpdateResult {
        /**
         * Represents a successful event update.
         *
         * @property event The updated event.
         */
        data class Success(val event: Event) : EventUpdateResult()

        /**
         * Represents an error during event update.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : EventUpdateResult()
    }
}
